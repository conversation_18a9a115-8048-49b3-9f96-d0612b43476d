<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item :name="1">
            <template slot="title">
              发货单详情<span class="title-code">{{
                basicFormModel.customerCreditCode
              }}</span>
            </template>

            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="供应商名称：" prop="customerName">
                    <el-input v-model="basicFormModel.customerName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发货单状态：" prop="status">
                    <el-tag v-if="deliveryFormModel.status == '1'" type="warning"
                    >待确认发货</el-tag
                    >
                    <el-tag v-if="deliveryFormModel.status == '2'"
                    >已确认发货</el-tag
                    >
                    <el-tag v-if="deliveryFormModel.status == '3'" type="success"
                    >对方已收货</el-tag
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item :name="2" title="明细信息" v-if="deliveryFormModel.status != 1">
            <el-table
              :data="renderData"
              class="main-table"
              border
              style="width: 100%"
              :span-method="handleSpanMethod"
              :row-key="getRowKey"
            >
              <!-- 序号 -->
              <el-table-column label="序号" width="60">
                <template slot-scope="scope">
                  <span class="sequence-number">{{ scope.row.mainIndex }}</span>
                </template>
              </el-table-column>

              <!-- 计划编号 -->
              <el-table-column prop="planNo" label="计划编号" width="200">
                <template slot-scope="scope">
                  <span>{{ scope.row.planNo }}</span>
                </template>
              </el-table-column>

              <!-- 计划交货时间 -->
              <el-table-column
                prop="planDeliveryTime"
                label="计划交货时间"
                width="120"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.planDeliveryTime }}</span>
                </template>
              </el-table-column>

              <!-- 物料名称 -->
              <el-table-column prop="materialName" label="物料名称(客)">
                <template slot-scope="scope">
                  <span>{{ scope.row.materialName }}</span>
                </template>
              </el-table-column>

              <!-- 计划数量 -->
              <el-table-column
                prop="planQuantity"
                label="计划数量(单位)"
                width="150"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.planQuantity }}{{ scope.row.unitName }}</span>
                </template>
              </el-table-column>

              <!-- 实发数量 -->
              <el-table-column
                prop="actualQuantity"
                label="实发数量(单位)"
                width="160"
              >
                <template slot-scope="scope">
                  <div class="input-group">
                    <el-input
                      v-model="scope.row.actualQuantity"
                      class="quantity-input"
                      size="small"
                      placeholder=""
                      disabled
                    />
                    <div class="unit-text">{{ scope.row.unitName }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 实收数量 -->
              <el-table-column
                prop="receiptQuantity"
                label="实收数量(单位)"
                width="160"
                v-if="!isDelivery"
              >
                <template slot-scope="scope">
                  <div class="input-group">
                    <el-input
                      v-model="scope.row.receiptQuantity"
                      class="quantity-input"
                      size="small"
                      placeholder="请输入实收数量"
                      @input="handleReceiptQuantityChange(scope.$index, scope.row, $event)"
                    />
                    <div class="unit-text">{{ scope.row.unitName }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 工业互联网标识码 -->
              <el-table-column
                prop="identificationCode"
                label="工业互联网标识码"
                min-width="200"
              >
                <template slot-scope="scope">
                  <span class="link-text">{{
                      scope.row.identificationCode
                    }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="herbProductName" label="药材产品名称">
              </el-table-column>

              <el-table-column prop="productionBatchNo" label="生产批号">
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <el-collapse-item :name="3" title="发货信息">
            <el-form
              ref="deliveryDetail"
              label-position="top"
              :model="deliveryFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="deliveryTime" label="发货时间：">
                    <el-date-picker
                      v-model="deliveryFormModel.deliveryTime"
                      type="date"
                      placeholder=""
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item prop="transportType" label="运输类型：">
                    <el-input v-model="deliveryFormModel.transportType" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="driverPhone" label="司机电话：">
                    <el-input
                      v-model="deliveryFormModel.driverPhone"
                      placeholder="请输入"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="licensePlate" label="车牌号：">
                    <el-input
                      v-model="deliveryFormModel.licensePlate"
                      placeholder="请输入"
                      max-length="20"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="deliveryRemark" label="发货备注：">
                    <el-input
                      type="textarea"
                      v-model="deliveryFormModel.deliveryRemark"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="delivery_image" label="图片：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in deliveryFormModel.deliveryImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="deliveryFormModel.deliveryImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item :name="4" title="收货作业" v-if="!isDelivery">
            <el-form
              ref="deliveryWork"
              label-position="top"
              :model="deliveryWorkForm"
            >
              <el-row :gutter="20" class="deliveryWork-row">
                <el-col :span="24">
                  <el-form-item prop="remark" label="收货备注：">
                    <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    />
                    <el-input
                      v-model="deliveryWorkForm.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注"
                      maxLength="100"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiptImages" label="图片：">
                    <upload-img
                      :limit-count="3"
                      :img-list.sync="deliveryWorkForm.receiptImages"
                    >
                      <template #tip>
                        请上传JPG或PNG格式，每张图片大小不超过4M，
                        最多可上传3张图片
                      </template>
                    </upload-img>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            v-if="deliveryFormModel.status == 3"
            :name="4"
            title="收货信息"
          >
            <el-form
              ref="deliveryWork"
              label-position="top"
              :model="deliveryWorkForm"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiptRemark" label="备注：">
                    <!-- <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    /> -->
                    <el-input
                      v-model="deliveryWorkForm.receiptRemark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注"
                      max-length="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiveImages" label="收货凭证：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in deliveryWorkForm.receiveImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="deliveryImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="save" v-if="!isDelivery">确认收货</el-button>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import acc from "@/api/acc/acc";
import UploadImg from "@/components/DataDialog/uploadImg.vue";

export default {
  name: "deliveryDetail",
  components: {UploadImg},
  data() {
    return {
      activeName: "first",
      collapse: [1, 2, 3, 4],
      isRemark: false,
      basicFormModel: {
        customerPrefix: "",
        customerName: "",
        customerCreditCode: "",
      },
      tableData: [],
      deliveryFormModel: {
        deliveryAddress: "",
        transportType: "",
        transportTypeName: "",
        driverPhone: "",
        licensePlate: "",
        deliveryRemark: "",
        deliveryImages: [],
      },
      deliveryWorkForm: {
        remark: "",
        receiptImages: [],
        receiptRemark:''
      },
      renderData:[],
      deliveryWorkRules: {
        remark: [
          {
            required: true,
            message: "请输入收货备注",
            trigger: "blur",
          },
        ],
        receiptImages: [
          {
            required: true,
            message: "请上传图片",
            trigger: "change",
          },
        ],
      },
      isDelivery: false
    };
  },
  created() {
    acc.queryDeliveryOrderDetail({
        deliveryOrderNo: this.$route.query.deliveryOrderNo,
      }).then((res) => {
        this.basicFormModel.customerName = res.data.customerName;
        this.basicFormModel.customerPrefix = res.data.customerPrefix;
        this.basicFormModel.customerCreditCode = res.data.customerCreditCode;
        this.deliveryFormModel = res.data;
        // this.deliveryFormModel.status = this.getStatusText(res.data.status);
        this.deliveryFormModel.transportType = this.getTransportTypeText(
          res.data.transportType
        );
        if (this.isDelivery) {
          this.deliveryWorkForm.receiptImages = res.data.receiptImages;
          this.deliveryWorkForm.receiptRemark = res.data.receiptRemark;
        }
        this.tableData = this.convertToFlatFormat(res.data.details);
        this.processTableData();
    });
    this.isDelivery = !!this.$route.query.isDelivery
  },
  methods: {
    back() {
      this.$router.replace({
        path: "arrivalPlan",
        query: { activeName: "first" },
      });
    },
    save() {
      if (!this.validateReceiptQuantities()) {
        return;
      }
      const data = {
        remarkSwitch: this.isRemark? 1 : 0,
        deliveryOrderNo: this.$route.query.deliveryOrderNo,
        remark: this.deliveryWorkForm.remark,
        receiptImages: this.deliveryWorkForm.receiptImages.map(
          (item) => item.url
        ),
        deliveryPlanList: this.tableData.map((item) => {
          return {
            receiptQuantity:Number(item.receiptQuantity),
            id: item.id,
          };
        }),
        deliveryImageList: !!this.deliveryFormModel.deliveryImages ? this.deliveryFormModel.deliveryImages.map(
          (item) => item
        ) : [],
      };
      console.log(data)
      acc.confirmReceiptOrder(data).then((res) => {
        this.$message.success("确认收货成功");
        this.$router.replace({
          path: "arrivalPlan",
          query: { activeName: "first" },
        });
      });
    },
    // 获取运输类型文本
    getTransportTypeText(type) {
      const typeMap = {
        1: "自有车辆",
        2: "第三方车辆",
      };
      return typeMap[type] || type || "";
    },
    // 处理表格数据，为合并单元格做准备
    async processTableData() {
      // 保持原有顺序，不重新分组排序
      this.renderData = [];
      let mainIndex = 1;
      const planNumberIndexMap = {}; // 记录每个计划编号的主序号

      // 按原有顺序处理数据
      this.tableData.forEach((item, index) => {
        // 检查是否是该计划编号的第一次出现
        const isFirstInGroup = !(item.planNo in planNumberIndexMap);

        if (isFirstInGroup) {
          planNumberIndexMap[item.planNo] = mainIndex;
          mainIndex++;
        }

        // 计算该计划编号的组大小
        const groupSize = this.tableData.filter(
          (data) => data.planNo === item.planNo
        ).length;

        // 计算在组内的索引
        const groupIndex =
          this.tableData
            .slice(0, index + 1)
            .filter((data) => data.planNo === item.planNo).length - 1;

        this.renderData.push({
          ...item,
          uniqueKey: `${item.planNo}_${index}`, // 使用原始索引确保唯一性
          mainIndex: isFirstInGroup ? planNumberIndexMap[item.planNo] : "", // 只有第一行显示主序号
          isFirstInGroup: isFirstInGroup,
          groupSize: groupSize,
          groupIndex: groupIndex,
          originalIndex: index,
          originalData: item,
        });
      });
    },

    // 将二维数据格式转换为一维数组
    convertToFlatFormat(groupedArray) {
      if (!Array.isArray(groupedArray) || groupedArray.length === 0) {
        return [];
      }

      const flatArray = [];

      groupedArray.forEach((group) => {
        if (
          group.deliveryOrderHerbInfoVOList &&
          Array.isArray(group.deliveryOrderHerbInfoVOList)
        ) {
          group.deliveryOrderHerbInfoVOList.forEach((item) => {
            flatArray.push({
              id: item.id || "",
              planNo: group.planNo || "",
              planDeliveryTime: group.planDeliveryTime || "",
              materialName: group.materialName || "",
              materialCode: group.materialCode || "",
              planQuantity: group.planQuantity || undefined,
              unit: item.unit || "",
              unitName: item.unitName || "",
              actualQuantity: item.actualQuantity || undefined,
              identificationCode: item.identificationCode || "",
              productName: group.materialName || "",
              herbProductName: item.herbProductName || "",
              herbProductCode: item.herbProductCode || "",
              productionBatchNo: item.productionBatchNo || "",
              deliveryOrderNo: item.deliveryOrderNo || "",
            });
          });
        }
      });

      return flatArray;
    },

    // 处理单元格合并
    handleSpanMethod({ row, columnIndex }) {
      // 需要合并的列索引：0-序号, 1-计划编号, 2-添加子项, 3-计划交货时间, 4-物料名称, 5-计划数量
      const mergeColumns = [0, 1, 2, 3, 4, 5];

      if (mergeColumns.includes(columnIndex)) {
        if (row.isFirstInGroup) {
          // 如果是组内第一行，返回合并的行数
          return {
            rowspan: row.groupSize,
            colspan: 1,
          };
        } else {
          // 如果不是组内第一行，隐藏该单元格
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }

      // 其他列不合并
      return {
        rowspan: 1,
        colspan: 1,
      };
    },
    getRowKey(row) {
      return row.uniqueKey;
    },
    handleReceiptQuantityChange(index, row, value) {
      // 更新 receiptQuantity
      this.$set(row, 'receiptQuantity', value);

      const originalIndex = row.originalIndex; // 来自 processTableData() 中定义的字段
      if (originalIndex !== undefined && this.tableData[originalIndex]) {
        this.$set(this.tableData[originalIndex], 'receiptQuantity', value);
      }
    },
    validateReceiptQuantities() {
      const invalidRows = this.tableData.filter(row => {
        // 检查 receiptQuantity 是否为空、null 或未定义
        return (
          row.receiptQuantity === null ||
          row.receiptQuantity === undefined ||
          row.receiptQuantity.toString().trim() === ''
        );
      });

      if (invalidRows.length > 0) {
        this.$message.error('请填写所有实收数量');
        return false;
      }
      return true;
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
.title-code {
  color: #8a8989;
  font-size: 12px;
  margin-left: 20px;
}
.deliveryWork-row.el-row {
  margin-bottom: 20px;
}
::v-deep .el-form-item__error {
  color: red !important;
  margin-top: 15px;
}
</style>
