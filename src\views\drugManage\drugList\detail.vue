<template>
  <div class="app-container">
    <el-container :style="{ height: '100%'}">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="产品基本信息" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :disabled="action==='detail'"
              :inline="true"
            >
              <el-form-item
                prop="medicinalMaterialsCode"
                label="中药材编码："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.medicinalMaterialsCode" placeholder="请输入中药材编码" />
              </el-form-item>

              <el-form-item
                prop="medicinalProductName"
                label="药材产品名称："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.medicinalProductName" placeholder="请输入药材产品名称" />
              </el-form-item>

              <el-form-item
                prop="freshFood"
                label="对应鲜品："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.freshFood" placeholder="请输入对应鲜品" />
              </el-form-item>

              <el-form-item
                prop="statisticUnits"
                label="药材统计单位："
                class="el-form-item-width"
              >
                <el-select v-model="basicFormModel.statisticUnits" placeholder="请输入药材统计单位">
                  <el-option value="kg" label="kg" />
                  <el-option value="g" label="g" />
                </el-select>
              </el-form-item>

              <el-form-item
                prop="medicinalAlias"
                label="药材别名："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.medicinalAlias" placeholder="请输入药材别名" />
              </el-form-item>

              <el-form-item
                prop="storageCondition"
                label="贮藏条件："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.storageCondition" placeholder="请输入储存条件" />
              </el-form-item>
              <el-row>
                <el-form-item
                  prop="productPresentation"
                  label="产品简介："
                  style="width: 98%"
                >
                  <el-input v-model="basicFormModel.productPresentation" :maxlength="200" type="textarea" :rows="3" placeholder="请输入产品简介" />
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item
                  prop="remark"
                  label="备注："
                  style="width: 98%"
                >
                  <el-input v-model="basicFormModel.remark" :maxlength="200" type="textarea" :rows="3" placeholder="请输入备注" />
                </el-form-item>
              </el-row>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="产品属性" :name="2">

            <virtual-table
              v-if="action==='detail'"
              :enable-search="false"
              auto-height
              :columns="specColumnsShow"
              :table-data="specTableData"
            />

            <div v-else>
              <virtual-table
                ref="attr-table"
                :enable-search="false"
                auto-height
                :columns="specColumns"
                :table-data="specTableData"
                :edit-rules="specRules"
              >
                <template v-slot:attrNameCn="{row, scope}">
                  <el-input
                    v-model="row.attrNameCn"
                    placeholder="请输入属性名"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>
                <template v-slot:attrNameEn="{row, scope}">
                  <el-input
                    v-model="row.attrNameEn"
                    placeholder="请输入属性英文名"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>
                <template v-slot:attrIndex="{row, scope}">
                  <el-input
                    v-model="row.attrIndex"
                    placeholder="请输入属性排序"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>
                <template v-slot:attrValue="{row, scope}">
                  <el-input
                    v-model="row.attrValue"
                    placeholder="请输入属性值"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>
                <template v-slot:attrType="{row, $index, scope}">
                  <el-select
                    v-model="row.attrType"
                    placeholder="请选择属性类型"
                    @change="$refs.table.updateStatus(scope)"
                  >
                    <el-option :value="10" label="公有" />
                    <el-option :value="20" label="私有" />
                  </el-select>
                </template>
                <template v-slot:operate="{$index}">
                  <span class="text_button" @click="deleteSpec($index)">删除</span>
                </template>
              </virtual-table>
              <demo-block
                message="添加属性"
                :icon-class="'el-icon-plus icon-class'"
                @click.native="addSpec"
              />
            </div>

          </el-collapse-item>
          <el-collapse-item title="产品图片" :name="3">
            <virtual-table
              ref="table"
              :enable-search="false"
              auto-height
              :columns="imgColumns"
              :table-data="imgTableData"
              :row-height="100"
            >
              <template v-slot:imgPath="{row}">
                <upload-img :img-list="[{ url: row.imgPath}]" readonly />
              </template>
              <template v-slot:operate="{$index}">
                <span class="text_button" @click="deleteImg($index)">删除</span>
              </template>
            </virtual-table>
            <demo-block
                v-if="action!=='detail'"
              message="添加图片"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addImg"
            />
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </el-footer>
    </el-container>
    <simple-data-dialog
      v-if="imgDialogVisible"
      title="添加产品图片"
      :visible="true"
      size="small"
    >
      <el-form
        ref="imgForm"
        label-position="top"
        :model="imgFormModel"
        :rules="imgFormRules"
        :inline="true"
      >
        <el-form-item
          prop="fileList"
          label="产品图片："
          class="el-form-item-width"
        >
          <upload-img :limit-count="1" :img-list.sync="imgFormModel.fileList" />
        </el-form-item>
        <el-form-item
          prop="remark"
          label="说明："
          style="width: 100%"
        >
          <el-input v-model="imgFormModel.remark" type="textarea" placeholder="请输入图片说明" />
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="imgDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveImage">保存</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>
<script>

import DemoBlock from '@/components/DemoBlock'
import VirtualTable from '@/components/VirtualTable/VirtualTable'
import UploadImg from '@/components/DataDialog/uploadImg'
import SimpleDataDialog from '@/components/SimpleDataDialog/index.vue'
import accApi from '@/api/acc/acc'

export default {
  name: 'ProductDetail',
  components: {
    SimpleDataDialog,
    UploadImg,
    DemoBlock,
    VirtualTable
  },
  data() {
    return {
      action: null,
      categoryList: [],
      beianTableData: [],
      beianDialogVisible: false,
      imgDialogVisible: false,
      collapse: [1, 2, 3],
      formRules: {
        materialName: [
          { required: true, message: '请输入物资名称', trigger: 'blur' }
        ]
      },
      // 表头
      specColumns: [
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '属性名',
          field: 'attrNameCn',
          slotName: 'attrNameCn'
        },
        {
          title: '属性英文名',
          field: 'attrNameEn',
          slotName: 'attrNameEn'
        },
        {
          title: '属性排序',
          field: 'attrIndex',
          slotName: 'attrIndex'
        },
        {
          title: '属性值',
          field: 'attrValue',
          slotName: 'attrValue'
        },
        {
          title: '属性类型',
          field: 'attrType',
          slotName: 'attrType'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      // 表头
      specColumnsShow: [
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '属性名',
          field: 'attrNameCn'
        },
        {
          title: '属性英文名',
          field: 'attrNameEn'
        },
        {
          title: '属性排序',
          field: 'attrIndex'
        },
        {
          title: '属性值',
          field: 'attrValue'
        },
        {
          title: '属性类型',
          field: 'attrType'
        }
      ],
      specRules: {
        attrNameCn: [
          { required: true, message: '请输入属性名' }
        ],
        attrNameEn: [
          { required: true, message: '请输入属性英文名' }
        ],
        attrIndex: [
          { required: true, message: '请输入属性排序号' }
        ],
        attrValue: [
          { required: true, message: '请输入属性值' }
        ],
        attrType: [
          { required: true, message: '请输入属性类型' }
        ]
      },
      specTableData: [],
      imgColumns: [{
        type: 'seq',
        title: '序号'
      },
      {
        title: '产品图片',
        field: 'imgPath',
        slotName: 'imgPath'
      },
      {
        title: '说明',
        field: 'remark'
      },
      {
        title: '操作',
        field: 'operate',
        slotName: 'operate',
        width: '50px'
      }
      ],
      imgTableData: [],
      imgFormModel: {
        fileList: [],
        remark: ''
      },
      imgFormRules: {
        fileList: { required: true, message: '请上传图片' },
        remark: { required: true, message: '请输入图片说明' }
      },
      basicFormModel: {
      },
      productImgList: [],
      basicFormRules: {
        medicinalMaterialsCode: [
          { required: true, message: '请输入中药材编码' }
        ], medicinalProductName: [
          { required: true, message: '请输入药材产品名称' }
        ], statisticUnits: [
          { required: true, message: '请输入药材统计单位' }
        ]
      }
    }
  },
  computed: {},
  mounted() {
    this.action = this.$route.query.action
    if (this.action === 'detail') {
      this.imgColumns.pop()
    }
    if (this.action === 'edit' || this.action === 'detail') {
      this.queryProduct()
    }
  },
  methods: {
    // 查询详情
    queryProduct() {
      accApi.queryDrug({ id: this.$route.query.id }).then(res => {
        const obj = res.data[0]
        this.basicFormModel = obj
        this.specTableData = obj.productAttrList
        this.imgTableData = obj.productImgList
      })
    },

    addSpec() {
      this.specTableData.push({
        attrType: null,
        attrValue: null,
        attrIndex: null,
        attrNameEn: null,
        attrNameCn: null
      })
    },
    addImg() {
      this.imgDialogVisible = true
      this.imgFormModel = {
        fileList: [],
        remark: ''
      }
    },
    deleteSpec(index) {
      this.specTableData.splice(index, 1)
    },
    deleteImg(index) {
      this.imgTableData.splice(index, 1)
    },
    back() {
      this.$router.replace('drugList')
    },
    submit() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          this.$refs['attr-table'].validate(attrValid => {
            if (attrValid) {
              const api = this.action === 'edit' ? 'editDrug' : 'addDrug'
              accApi[api]({
                ...this.basicFormModel,
                id: this.$route.query.id,
                productAttrList: this.specTableData,
                productImgList: this.imgTableData
              }).then(res => {
                this.$message.success(res.message)
                this.back()
              })
            }
          })
        }
      })
    },
    saveImage() {
      this.$refs['imgForm'].validate(valid => {
        if (valid) {
          this.imgTableData.push({
            imgPath: this.imgFormModel.fileList[0].url,
            remark: this.imgFormModel.remark
          })
          this.imgDialogVisible = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-select--small, .el-cascader--small, .el-input-number--small {
  width: 100%
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>
