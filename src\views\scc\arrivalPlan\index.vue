<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="到货计划列表" name="first">
        <data-select
          :search-data.sync="search"
          :button-data="buttonData"
          @return-search="
            (data) => {
              searchHelper.search(data);
            }
          "
          @return-reset="searchHelper.reset"
        >
        </data-select>

        <data-table
          ref="dataTable"
          :table-data="searchHelper.dataList"
          :column="column"
          :pagination.sync="searchHelper.pagination"
          :table-option="tableOption"
          @search-event="
            () => {
              searchHelper.handleQuery();
            }
          "
        >
          <template v-slot:planStatus="{ row }">
            <el-tag v-if="row.planStatus == 1" type="warning">{{
              statusMap[row.planStatus]
            }}</el-tag>
            <el-tag v-if="row.planStatus == 2" type="primary">{{
              statusMap[row.planStatus]
            }}</el-tag>
            <el-tag v-if="row.planStatus == 3" type="success">{{
              statusMap[row.planStatus]
            }}</el-tag>
          </template>
        </data-table>
      </el-tab-pane>
      <el-tab-pane label="来货单据列表" name="second">
        <data-select
          :search-data.sync="receiptSearch"
          @return-search="
            (data) => {
              receiptHelper.search(data);
            }
          "
          @return-reset="receiptHelper.reset"
        >
        </data-select>

        <data-table
          ref="dataTable"
          :table-data="receiptHelper.dataList"
          :column="receiptColumn"
          :pagination.sync="receiptHelper.pagination"
          :table-option="tableOption"
          @search-event="
            () => {
              receiptHelper.handleQuery();
            }
          "
        >
          <template v-slot:orderStatus="{ row }">
            <el-tag v-if="row.orderStatus == 2" type="warning">{{
              receiptStatusMap[row.orderStatus]
            }}</el-tag>
            <el-tag v-if="row.orderStatus == 3" type="success">{{
              receiptStatusMap[row.orderStatus]
            }}</el-tag>
          </template>
        </data-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DataSelect from "@/components/DataSelect/index.vue";
import accApi from "@/api/acc/acc";
import DataTable from "@/components/DataTable/index.vue";

export default {
  name: "ArrivalPlan",
  components: { DataTable, DataSelect },
  data() {
    return {
      tableOption: {
        option: {
          height: "calc(100vh - 250px)",
        },
      },
      activeName: "first",
      search: {
        searchText: {
          label: "供货商名称/物料名称/关联采购单号",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入供货商名称/物料名称/关联采购单号",
          },
        },
        planStatus: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "1", label: "待发货" },
              { value: "2", label: "已发货" },
              { value: "3", label: "已收货" },
            ],
            placeholder: "请选择状态",
          },
        },
        planDeliveryTime: {
          label: "计划交货时间",
          value: null,
          type: "date",
          option: {
            placeholder: "请选择时间",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
        },
      },
      buttonData: [
        {
          label: "添加",
          action: this.onAddClick,
          permission: "all",
        },
      ],
      searchHelper: new this.$searchHelper({ api: accApi.queryDeliveryPlan }),
      receiptHelper: new this.$searchHelper({ api: accApi.queryReceiveList }),
      column: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "计划编号",
            prop: "planCode",
            sortable: false,
          },
          {
            label: "计划状态",
            prop: "planStatus",
            sortable: false,
            slotName: "planStatus",
          },
          {
            label: "物料编码",
            prop: "materialCode",
            sortable: false,
          },
          {
            label: "物料名称",
            prop: "materialName",
            sortable: false,
          },
          {
            label: "计划数量",
            prop: "planQuantity",
            sortable: false,
          },
          {
            label: "单位",
            prop: "unit",
            sortable: false,
          },
          {
            label: "计划交货日期",
            prop: "planDeliveryTime",
            sortable: false,
          },
          {
            label: "供应商姓名",
            prop: "supplierName",
            sortable: false,
          },
          {
            label: "关联采购单号",
            prop: "purchaseOrderNo",
            sortable: false,
          },
          {
            label: "备注",
            prop: "remark",
            sortable: false,
          },
          {
            label: "收获作业人",
            prop: "receiptOperator",
            sortable: false,
          },
          {
            label: "收获时间",
            prop: "deliveryTime",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: (row) => {
            let button = [];
            switch (row.planStatus) {
              case "1":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "撤销",
                    action: this.onCancel,
                    permission: "all",
                  },
                ];
                break;
              case "2":
                button = [
                  {
                    label: "收货作业",
                    action: this.onHarvest,
                    permission: "all",
                  },
                ];
                break;
              case "3":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "删除",
                    action: this.onDelete,
                    permission: "all",
                  },
                ];
                break;
            }
            return button;
          },
        },
      },
      statusMap: {
        1: "待发货",
        2: "已发货",
        3: "已收货",
      },
      receiptStatusMap: {
        2: "待确认发货",
        3: "已确认收货",
      },
      receiptSearch: {
        searchText: {
          label: "供应商名称/发货单号",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入供应商名称/发货单号",
          },
        },
        orderStatus: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "2", label: "待确认发货" },
              { value: "3", label: "已确认收货" },
            ],
            placeholder: "请选择状态",
          },
        },
      },
      receiptColumn: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "发货单号",
            prop: "deliveryOrderNo",
            sortable: false,
          },
          {
            label: "单据状态",
            prop: "orderStatus",
            sortable: false,
            slotName: "orderStatus",
          },
          {
            label: "品类数",
            prop: "typeNum",
            sortable: false,
          },
          {
            label: "供应商名称",
            prop: "companyName",
            sortable: false,
          },
          {
            label: "发货时间",
            prop: "deliveryTime",
            sortable: false,
          },
          {
            label: "发货方备注",
            prop: "deliveryRemark",
            sortable: false,
          },
          {
            label: "收货方备注",
            prop: "remark",
            sortable: false,
          },
          {
            label: "收货作业人",
            prop: "receiptOperator",
            sortable: false,
          },
          {
            label: "收货时间",
            prop: "receiptTime",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: (row) => {
            let button = [];
            switch (row.orderStatus) {
              case "2":
                button = [
                  {
                    label: "收货作业",
                    action: this.onHarvest,
                    permission: "all",
                  },
                ];
                break;
              case "3":
                button = [
                  {
                    label: "详情",
                    action: this.onDeliveryDetail,
                    permission: "all",
                  },
                  {
                    label: "删除",
                    action: this.onReceiptDelete,
                    permission: "all",
                  },
                ];
                break;
            }
            return button;
          },
        },
      },
    };
  },
  created() {
    this.activeName = this.$route.query.activeName || "first";
    this.searchHelper.handleQuery();
  },
  methods: {
    handleTabClick() {
      if (this.activeName === "first") {
        this.searchHelper.handleQuery();
      } else {
        this.receiptHelper.handleQuery();
      }
    },
    onAddClick() {
      this.$router.replace({
        path: "arrivalPlanAdd",
        query: { action: "add" },
      });
    },
    onDetail(row) {
      this.$router.replace({
        path: "arrivalPlanDetail",
        query: { action: "detail", id: row.id },
      });
    },
    onDelete(row) {
      this.$confirm("是否删除？", "提示", { type: "warning" }).then(() => {
        accApi.delDeliveryPlan({ id: row.id }).then((res) => {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    onCancel(row) {
      accApi.cancelDeliveryPlan({ id: row.id }).then((res) => {
        if (res.code === "PARAM_VALIDATE_ERROR_RELATED") {
          this.$confirm(res.msg, "", {
            confirmButtonText: "确定",
            type: "warning",
            center: true,
            showCancelButton: false,
          })
            .then(() => {})
            .catch(() => {});
        } else {
          this.$confirm("是否确定撤销？", "提示", { type: "warning" }).then(
            () => {
              accApi.cancelDeliveryPlan({ id: row.id }).then((res) => {
                this.$message({
                  message: "撤销成功",
                  type: "success",
                });
                this.searchHelper.handleQuery();
              });
            }
          );
        }
      });
    },
    onHarvest(row) {
      this.$router.replace({
        path: "deliveryDetail",
        query: { action: "operation", deliveryOrderNo: row.deliveryOrderNo },
      });
    },
    onDeliveryDetail(row) {
      this.$router.replace({
        path: "deliveryDetail",
        query: { action: "deliveryDetail", deliveryOrderNo: row.deliveryOrderNo, isDelivery: true },
      });
    },
    onReceiptDelete( row) {
      this.$confirm("是否删除？", "提示", { type: "warning" }).then(() => {
        accApi
          .deleteDeliveryOrderList({ deliveryOrderNo: row.deliveryOrderNo })
          .then((res) => {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.receiptHelper.handleQuery();
          });
      });
    }
  },
};
</script>
