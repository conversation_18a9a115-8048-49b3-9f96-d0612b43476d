import request from '@/utils/request'

const ACC_BASE_URL = '/api/acc'
const accApi = {
  // 修改密码
  changePassword(data) {
    return request.post(`${ACC_BASE_URL}/userinfo/changePassword`, data)
  },
  // 校验用户密码是否是初始密码
  checkUserPassWord() {
    return request.get(`${ACC_BASE_URL}/userinfo/checkUserPassWord`)
  },
  // 码表
  codeQueryList(codeType) {
    return request.get(`${ACC_BASE_URL}/common/code/list`, { params: { codeType } })
  },

  // 查询角色列表
  getRoleList(data) {
    return request.get(`${ACC_BASE_URL}/userRole/queryUserRoleInfo`, { params: data })
  },
  //  标识规则
  tagRuleDateListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/rule/pageList`, data)
  },
  addTagRuleDateApi(data) {
    return request.post(`${ACC_BASE_URL}/api/rule/add`, data)
  },
  editTagRuleDateApi(data) {
    return request.post(`${ACC_BASE_URL}/api/rule/edit`, data)
  },
  detailTagRuleApi(data) {
    return request.get(`${ACC_BASE_URL}/api/rule/getById?id=${data.id}`, data)
  },
  exportExcelApi(params) {
    return request.get(`${ACC_BASE_URL}/api/productBatch/exportFlagCode`, { params, responseType: 'blob' })
  },
  delTagRuleApi(data) {
    return request.post(`${ACC_BASE_URL}/api/rule/deleteById`, data)
  },
  // 角色信息查询-根据用户编码
  queryUserRoleInfoWithUserCode(userCode) {
    return request.get(`${ACC_BASE_URL}/userRole/queryUserRoleInfoWithUserCode`, {
      params: {
        userCode,
        paramCheck: true
      }
    })
  },
  // 校验角色名称
  checkRoleName(data) {
    return request.get(`${ACC_BASE_URL}/userRole/checkRoleName`, { params: data })
  },
  // 新增角色
  addRole(data) {
    return request.post(`${ACC_BASE_URL}/userRole/addRole`, data)
  },
  // 修改角色
  editRole(data) {
    return request.put(`${ACC_BASE_URL}/userRole/editRole/${data.guid}`, data)
  },
  // 删除角色
  deleteRole(guid) {
    return request.delete(`${ACC_BASE_URL}/userRole/delRole/${guid}`)
  },
  // 保存角色菜单
  saveRoleMenu(data) {
    return request.post(`${ACC_BASE_URL}/userRole/batchSaveRoleMenu`, data)
  },
  // 查询菜单列表树
  queryMenuInfos() {
    return request.get(`${ACC_BASE_URL}/userRole/queryAccMenusList`)
  },
  // 角色授权菜单列表
  queryMenuInfoWithRole(params) {
    return request.get(`${ACC_BASE_URL}/userRole/queryAccMenus/${params.roleCode}?relationType=${params.relationType}`, params)
  },
  // 角色对应用户信息查询
  queryUserInfoWithRole(roleCode) {
    return request.get(`${ACC_BASE_URL}/userRole/queryUserInfoWithRoleCode/${roleCode}`)
  },
  // 查询用户信息for 角色页面
  queryUserInfoForRole(params) {
    return request.post(`${ACC_BASE_URL}/userinfo/queryListForRole`, params)
  },
  // 保存用户角色
  saveUserRole(data) {
    return request.post(`${ACC_BASE_URL}/userRole/batchSaveUserRole`, data)
  },
  // 保存用户和角色对应关系
  batchSaveUserRoleInfo(data) {
    return request.post(`${ACC_BASE_URL}/userRole/batchSaveUserRoleInfo`, data)
  },
  // 用户信息
  userInfo: {
    queryList(params) {
      return request.post(`${ACC_BASE_URL}/userinfo/queryList`, params)
    },
    add(data) {
      return request.post(`${ACC_BASE_URL}/userinfo/add`, data)
    },
    edit(emplCode, data) {
      return request.put(`${ACC_BASE_URL}/userinfo/edit/${emplCode}`, data)
    },
    queryUserDetail(emplCode) {
      return request.get(`${ACC_BASE_URL}/userinfo/queryUserDetail/${emplCode}`)
    },
    queryUserInit() {
      return request.get(`${ACC_BASE_URL}/userinfo/queryUserInit`)
    },
    resetPassword(emplCode) {
      return request.put(`${ACC_BASE_URL}/userinfo/resetPassword/${emplCode}`)
    },
    isResetStatus(emplCode, isDisable) {
      return request.put(`${ACC_BASE_URL}/userinfo/isResetStatus/${emplCode}/${isDisable}`)
    },
    // 编辑用户头像
    editUserAvatar(data) {
      return request.post(`${ACC_BASE_URL}/userinfo/addUserPictureUrl`, data)
    },
    // 导出
    exportUserExcel(params) {
      return request.post(`${ACC_BASE_URL}/userinfo/export`, params, { responseType: 'blob' })
    }
  },
  // 用户权限信息
  userPermission: {
    // 查询全量权限类型
    getAllPermissions(data) {
      return request.post(`${ACC_BASE_URL}/userpermission/info`, data)
    },
    savePermissions(data) {
      return request.post(`${ACC_BASE_URL}/userpermission/save`, data)
    },
    getPermissionByLoginUser(type) {
      return request.post(`${ACC_BASE_URL}/userpermission/permissions?type=${type}`)
    }

  },
  queryMenuConfList() {
    return request.get(`${ACC_BASE_URL}/menuConf/queryList`)
  },
  saveOrUpdateMenu(data) {
    return request.post(`${ACC_BASE_URL}/menuConf/saveOrUpdateMenu`, data)
  },
  deleteMenuConf(menuId) {
    return request.delete(`${ACC_BASE_URL}/menuConf/deleteMenu/${menuId}`)
  },
  dictionaryListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/sysDictype/pageList`, data)
  },
  dictionaryZiListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/sysDict/pageList`, data)
  },
  dictionaryApi(data) {
    return request.post(`${ACC_BASE_URL}/api/sysDict/list`, data)
  },
  dictionaryAllListApi(data) {
    return request.get(`${ACC_BASE_URL}/api/sysDictype/list`, data)
  },
  addDictionaryZiApi(data) {
    return request.post(`${ACC_BASE_URL}/api/sysDict/add`, data)
  },
  editDictionaryZiApi(data) {
    return request.post(`${ACC_BASE_URL}/api/sysDict/edit`, data)
  },
  detailDictionaryZiApi(data) {
    return request.get(`${ACC_BASE_URL}/api/sysDict/getById?id=${data.id}`, data)
  },
  deleteByIdDictionaryZiApi(data) {
    return request.post(`${ACC_BASE_URL}/api/sysDict/deleteById`, data)
  },
  uploadFileApi(data) {
    return request.post(`${ACC_BASE_URL}/file/uploadFile`, data, {
      headers: {
        'Content-Type': 'multipart/form-data;charset=UTF-8'
      }
    })
  },
  productBatchPageListList(data) {
    return request.post(`${ACC_BASE_URL}/api/productBatch/pageList`, data)
  },
  // 批次下拉框
  productBatchSelectListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/productBatch/list`, data)
  },
  // 产品模板下拉框
  productTemplateSelectListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/productTemplate/list`, data)
  },
  // 产品规则
  ruleListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/rule/list`, data)
  },
  productList(data) {
    return request.post(`${ACC_BASE_URL}/api/product/list`, data)
  },
  productDetailList(data) {
    return request.post(`${ACC_BASE_URL}/api/product/listWithDetail`, data)
  },
  addProductBatch(data) {
    return request.post(`${ACC_BASE_URL}/api/productBatch/add`, data)
  },
  deleteProductBatch(data) {
    return request.post(`${ACC_BASE_URL}/api/productBatch/deleteById`, data)
  },
  // 标识明细
  tagDetailDateListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/code/pageList`, data)
  },
  exportTagDetailApi(params) {
    return request.get(`${ACC_BASE_URL}/api/code/export`, { params, responseType: 'blob' })
  },
  detailTagDetailApi(data) {
    return request.get(`${ACC_BASE_URL}/api/code/getById?id=${data.id}`, data)
  },
  detailTagDetailApiByCode(data) {
    return request.get(`${ACC_BASE_URL}/api/code/getByCode?idisCode=${data.code}`, data)
  },
  //  标识生成
  tagGenerateDateListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/codeRecord/pageList`, data)
  },
  addTagGenerate(data) {
    return request.post(`${ACC_BASE_URL}/api/codeRecord/add`, data)
  },
  addTagGenerateCode(data) {
    return request.post(`${ACC_BASE_URL}/api/code/add`, data)
  },
  editTagGenerate(data) {
    return request.post(`${ACC_BASE_URL}/api/codeRecord/edit`, data)
  },
  exportTagGenerateApi(params) {
    return request.get(`${ACC_BASE_URL}/api/codeRecord/downloadCode`, { params, responseType: 'blob' })
  },

  productPageListList(data) {
    return request.post(`${ACC_BASE_URL}/api/product/pageList`, data)
  },
  productListAll(data) {
    return request.post(`${ACC_BASE_URL}/api/product/list`, data)
  },
  productCategoryList(data) {
    return request.post(`${ACC_BASE_URL}/api/productCategory/list`, data)
  },
  addProduct(data) {
    return request.post(`${ACC_BASE_URL}/api/product/add`, data)
  },
  editProduct(data) {
    return request.post(`${ACC_BASE_URL}/api/product/edit`, data)
  },
  queryProduct(params) {
    return request.get(`${ACC_BASE_URL}/api/product/getById`, { params })
  },
  delProduct(data) {
    return request.post(`${ACC_BASE_URL}/api/product/deleteById`, data)
  },
  queryBeiAnAttrList(data) {
    return request.post(`${ACC_BASE_URL}/api/productAttr/list`, data)
  },
  addProductAttr(data) {
    return request.post(`${ACC_BASE_URL}/api/productAttr/add`, data)
  },
  delProductAttr(data) {
    return request.post(`${ACC_BASE_URL}/api/productAttr/deleteById`, data)
  },
  getTemplatesByProductsList(data) {
    return request.post(`${ACC_BASE_URL}/api/productTemplate/getTemplatesByProductsList`, data)
  },
  getRuleProductList(data) {
    return request.post(`${ACC_BASE_URL}/api/rule/getRuleProductList`, data)
  },
  medicinalProductEdit(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/edit`, data)
  },
  productTemplatePageList(data) {
    return request.post(`${ACC_BASE_URL}/api/productTemplate/pageList`, data)
  },
  addProductTemplate(data) {
    return request.post(`${ACC_BASE_URL}/api/productTemplate/add`, data)
  },
  editProductTemplate(data) {
    return request.post(`${ACC_BASE_URL}/api/productTemplate/edit`, data)
  },
  deleteProductTemplate(data) {
    return request.post(`${ACC_BASE_URL}/api/productTemplate/deleteById`, data)
  },
  getProductTemplate(params) {
    return request.get(`${ACC_BASE_URL}/api/productTemplate/getById`, { params })
  },
  queryTemplateProduct(params) {
    return request.get(`${ACC_BASE_URL}/api/product/queryTemplateProduct`, { params })
  },
  saveScanRecord(data) {
    return request.post(`${ACC_BASE_URL}/api/scanCodeRecord/add`, data)
  },
  // 扫码
  quantityDisplayListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/scanCodeRecord/quantityDisplayList`, data)
  }, // 区域扫码
  areaScanningCodeList(data) {
    return request.post(`${ACC_BASE_URL}/api/scanCodeRecord/areaScanningCodeList`, data)
  }, // 一周标识生成量
  weeklyCodeList(data) {
    return request.post(`${ACC_BASE_URL}/api/scanCodeRecord/weeklyCodeList`, data)
  },
  // 扫描标识排行
  scanCodeRankingList(params) {
    return request.get(`${ACC_BASE_URL}/api/scanCodeRecord/scanCodeRankingList`, { params })
  }, // 客户端统计
  clientTypeList(params) {
    return request.get(`${ACC_BASE_URL}/api/scanCodeRecord/clientTypeList`, { params })
  }, // 产品排行
  productRankingList(data) {
    return request.post(`${ACC_BASE_URL}/api/scanCodeRecord/productRankingList`, data)
  }, // 列表查询
  recordPageList(data) {
    return request.post(`${ACC_BASE_URL}/api/scanCodeRecord/pageList`, data)
  }, // 列表查询
  downloadQRCode(data) {
    return request.post(`${ACC_BASE_URL}/api/codeRecord/downloadQRCode`, data)
  }, // 扫描地图
  scanMapList() {
    return request.get(`${ACC_BASE_URL}/api/scanCodeRecord/scanMapList`)
  }, // 扫描地图
  getProductInfo(params) {
    return request.get(`${ACC_BASE_URL}/api/code/getById`, { params })
  },
  getApiTicket(params) {
    return request.get(`${ACC_BASE_URL}/api/wx/getSignature`, { params })
  },
  checkCode(params) {
    return request.get(`${ACC_BASE_URL}/api/securityCode/check`, { params })
  },
  getInfoXg(params) {
    return request.get(`${ACC_BASE_URL}/api/securityCode/queryFarmer`, { params })
  },
  farmerPageList(data) {
    return request.post(`${ACC_BASE_URL}/api/farmer/pageList`, data)
  },
  getFarmerInfo(params) {
    return request.get(`${ACC_BASE_URL}/api/farmer/getById`, { params })
  },
  addFarmer(data) {
    return request.post(`${ACC_BASE_URL}/api/farmer/add`, data)
  },
  editFarmer(data) {
    return request.post(`${ACC_BASE_URL}/api/farmer/edit`, data)
  },
  deleteFarmer(data) {
    return request.post(`${ACC_BASE_URL}/api/farmer/deleteById`, data)
  },
  farmerProductRelationList(data) {
    return request.post(`${ACC_BASE_URL}/api/farmerProductRelation/list`, data)
  },
  // 部门管理
  departmentListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/pmcpDept/pageList`, data)
  },
  departmentAllListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/pmcpDept/list`, data)
  },
  addDepartmentApi(data) {
    return request.post(`${ACC_BASE_URL}/api/pmcpDept/add`, data)
  },
  editDepartmentApi(data) {
    return request.post(`${ACC_BASE_URL}/api/pmcpDept/edit`, data)
  },
  detailDepartmentApi(data) {
    return request.get(`${ACC_BASE_URL}/api/pmcpDept/getById?id=${data.id}`, data)
  },
  deleteDepartmentApi(data) {
    return request.post(`${ACC_BASE_URL}/api/pmcpDept/deleteById`, data)
  },
  operationListApi(data) {
    return request.post(`${ACC_BASE_URL}/api/qfOperationRecord/pageList`, data)
  },
  // 企业信息注册
  comApi(data) {
    return request.get(`${ACC_BASE_URL}/api/sysDict/largeDic?type=${data.type}`, data)
  },
  getCompanyInfoApi(data) {
    return request.get(`${ACC_BASE_URL}/api/companyInfo/getCompanyInfo`, data)
  },
  // 入驻角色
  getEntRoleList(data) {
    return request.post(`${ACC_BASE_URL}/userRole/getEntRoleList`, data)
  },
  companyInfoAddApi(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/report`, data)
  },
  companyInfoEditApi(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/editReport`, data)
  },
  companyInfoSubmitApi(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/submit`, data)
  },
  companyInfoUnSubmitApi(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/unSubmit`, data)
  },
  //   药材产品列表
  drugList(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/pageList`, data)
  },
  //   药材产品列表
  drugListAll(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/list`, data)
  },
  addDrug(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/add`, data)
  },
  editDrug(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/edit`, data)
  },
  queryDrug(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/listWithDetail`, data)
  },
  delDrug(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/deleteById`, data)
  },
  exportDrugApi(params) {
    return request.get(`${ACC_BASE_URL}/api/medicinalProduct/exportTemplate`, { params, responseType: 'blob' })
  },
  importDrugApi(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalProduct/importData`, data, {
      headers: {
        'Content-Type': 'multipart/form-data;charset=UTF-8'
      }
    })
  },
  //  企业审核
  queryAuditList(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/pageList`, data)
  },
  //  企业审核
  queryCompanyById(params) {
    return request.get(`${ACC_BASE_URL}/api/companyInfo/getById`, { params })
  },
  //  企业审核审核通过
  companyAuditPass(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/auditPass`, data)
  },
  //  企业审核审核驳回
  companyAuditReject(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/auditReject`, data)
  },
  //   药材批号列表
  medicinalBatchList(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/pageList`, data)
  },
  addMedicinalBatch(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/add`, data)
  },
  editMedicinalBatch(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/edit`, data)
  },
  queryMedicinalBatch(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/listDetail`, data)
  },
  delMedicinalBatch(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/deleteById`, data)
  },
  exportMedicinalBatchApi(params) {
    return request.get(`${ACC_BASE_URL}/api/medicinalBatch/exportTemplate`, { params, responseType: 'blob' })
  },
  importMedicinalBatchApi(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/importData`, data, {
      headers: {
        'Content-Type': 'multipart/form-data;charset=UTF-8'
      }
    })
  },
  medicinalBatchFile(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatchFile/add`, data)
  },
  queryMaterialList(data) {
    return request.post(`${ACC_BASE_URL}/api/material/pageList`, data)
  },
  queryMaterialDetail(data) {
    return request.get(`${ACC_BASE_URL}/api/material/detail`, { params: data })
  },
  addMaterial(data) {
    return request.post(`${ACC_BASE_URL}/api/material/add`, data)
  },
  editMaterial(data) {
    return request.post(`${ACC_BASE_URL}/api/material/edit`, data)
  },
  delMaterial(data) {
    return request.post(`${ACC_BASE_URL}/api/material/deleteById`, data)
  },
  // 客户管理
  queryCustomerList(data) {
    return request.post(`${ACC_BASE_URL}/api/customer/relation/page`, data)
  },
  queryCustomerDetail(data) {
    return request.get(`${ACC_BASE_URL}/api/customer/relation/get`, { params: data })
  },
  addCustomer(data) {
    return request.post(`${ACC_BASE_URL}/api/customer/relation/add`, data)
  },
  editCustomer(data) {
    return request.post(`${ACC_BASE_URL}/api/customer/relation/edit`, data)
  },
  delCustomer(data) {
    return request.get(`${ACC_BASE_URL}/api/customer/relation/delete`, { params: data })
  },
  updateCustomer(data) {
    return request.post(`${ACC_BASE_URL}/api/customer/relation/updateStatus`, data)
  },
  // 供应商管理
  querySupplierList(data) {
    return request.post(`${ACC_BASE_URL}/api/supplier/relation/page`, data)
  },
  querySupplierDetail(data) {
    return request.get(`${ACC_BASE_URL}/api/supplier/relation/get`, { params: data })
  },
  addSupplier(data) {
    return request.post(`${ACC_BASE_URL}/api/supplier/relation/add`, data)
  },
  editSupplier(data) {
    return request.post(`${ACC_BASE_URL}/api/supplier/relation/edit`, data)
  },
  delSupplier(data) {
    return request.get(`${ACC_BASE_URL}/api/supplier/relation/delete`, { params: data })
  },
  updateSupplier(data) {
    return request.post(`${ACC_BASE_URL}/api/supplier/relation/updateStatus`, data)
  },
  queryCompanyInfo(data) {
    return request.post(`${ACC_BASE_URL}/api/companyInfo/queryCompany`, data)
  },
  // 到货计划
  queryDeliveryPlan(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/plan/page`, data)
  },
  // 新增到货计划
  addDeliveryPlan(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/add`, data)
  },
  // 到货计划详情
  queryDeliveryPlanDetail(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/plan/detail`, { params: data })
  },
  // 到货计划删除
  delDeliveryPlan(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/delete`, { params: data })
  },
  // 到货计划撤销
  cancelDeliveryPlan(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/plan/revoke`, { params: data })
  },
  // 物料选择
  selectMaterial(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/select/material`, { params: data })
  },
  // 选择供应商
  selectSupplier(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/select/supplier`, { params: data })
  },
  // 来货
  queryReceiveList(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/order/page`, data)
  },
  // 供货计划协同
  queryCooperationLList(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/planCooperation/page`, data)
  },
  queryCooperationOrderLList(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/planCooperation/order/page`, data)
  },
  // 批次列表查询
  queryMedicinalBatchList(data) {
    return request.post(`${ACC_BASE_URL}/api/medicinalBatch/list`, data)
  },
  // 创建发货单
  createDeliveryOrder(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/planCooperation/order/create`, data)
  },
  // 发货单详情
  queryDeliveryOrderDetail(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/planCooperation/order/detail`, { params: data })
  },
  // 更新发货单
  updateDeliveryOrder(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/planCooperation/order/update`, data)
  },
  // 确认收货
  confirmReceiptOrder(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/receipt`, data)
  },
  // 删除计划
  deleteDeliveryPlan(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/planCooperation/delete`, data)
  },
  // 删除供货计划列表
  deleteDeliveryPlanList(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/planCooperation/invisible`, { params: data })
  },
  // 查询计划
  queryDeliveryPlanAll(data) {
    return request.post(`${ACC_BASE_URL}/api/delivery/planCooperation/selected/list`, data)
  },
  // 计划详情
  getDeliveryPlanDetail(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/plan/detail`, { params: data })
  },
  // 删除发货单
  deleteDeliveryOrderList(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/planCooperation/order/deleteOrderNo`, { params: data })
  },
  // 生成发货单PDF并下载
  generateDeliveryPDF(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/planCooperation/order/deliveryOrderNo/pdf`, { params: data })
  },
  // 设置供货计划供应商不可见
  setSupplierInvisible(data) {
    return request.get(`${ACC_BASE_URL}/api/delivery/planCooperation/receiver/list`, { params: data })
  },
}

export default accApi
