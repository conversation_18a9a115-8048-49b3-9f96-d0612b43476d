<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    >
      <template #queryButton>
        <upload-file style="width: 130px" :on-change.sync="onChange" btn-text="批量导入批次信息" />
        <upload-file style="width: 130px;margin-left: 8px" :on-change.sync="onUploadPdfMany" btn-text="批量上传质检信息" />
      </template>

    </data-select>
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />
    <simple-data-dialog
      v-if="showVisible"
      title="工业码"
      :visible="true"
      size="small"
    >
      <el-button type="primary" @click="downCodeText">下载码txt</el-button>
      <el-button type="primary" @click="downCodePic">下载二维码</el-button>
      <el-footer class="button-container">
        <el-button @click="showVisible = false">取消</el-button>
      </el-footer>

    </simple-data-dialog>
    <simple-data-dialog
      ref="pdf"
      title="批次质检管理"
      :visible.sync="visible"
      size="small"
      class="quality-dialog"
    >
      <div class="upload-container">
        <!-- 文件预览区域 -->
        <div v-if="fileInfo" class="file-preview" style="position:relative ">
          <div class="preview-item">
            <!-- 修改这里，使用 PDF 预览替代图标 -->
            <div class="pdf-preview">
              <iframe :src="fileInfo.url" width="100%" height="250" />
            </div>
            <div class="file-name" style="display: flex;justify-content: space-between">
              <div class="file-name">{{ fileInfo.name }}</div>
              <div> <i class="el-icon-download" style="font-size: 16px" @click="handleRemove" /></div>
            </div>
          </div>
          <div class="preview-item-move">
            <div>
              <el-button icon="el-icon-view" type="primary" class="reupload-btn" @click="handleOpenPdf(fileInfo.url)">
                查看详情
              </el-button></div>
            <div> <el-button icon="el-icon-refresh-right" type="primary" class="reupload-btn" @click="handleReupload">
              重新上传
            </el-button></div>

          </div>

        </div>

        <!-- 上传区域
       -->
        <el-upload
          v-else
          ref="uploadRef"
          class="upload-area"
          action="#"
          :http-request="beforeUpload"
          :auto-upload="true"
          :show-file-list="false"
          accept=".pdf"
        >
          <div class="upload-content">
            <i class="el-icon-upload" />
            <div class="upload-text">点击或拖拽文件到此处上传</div>
          </div>
        </el-upload>
      </div>

      <!-- 底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false;fileInfo = null">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import uploadFile from '@/components/DataDialog/uploadFileList.vue'
import acc from '@/api/acc/acc'
import accApi from '@/api/acc/acc'
import query from '@/components/mixin/query'
import SimpleDataDialog from '@/components/SimpleDataDialog/index.vue'
import commonApi from '@/api/common/common'
export default {
  name: 'Product',
  components: { SimpleDataDialog, DataSelect, DataTable, uploadFile },
  mixins: [query],
  data() {
    return {
      visible: false,
      pdfId: '',
      fileInfo: null,
      beianDialogVisible: false,
      baseFrom: {},
      specColumns: [
        {
          title: '规格名称',
          field: 'attrNameCn',
          slotName: 'attrNameCn'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      specTableData: [],
      specRules: {
        attrNameCn: [
          { required: true, message: '请输入规格名称' }
        ]
      },

      leavelColumns: [
        {
          title: '等级名称',
          field: 'attrNameCn',
          slotName: 'attrNameCn'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      leavelTableData: [],
      leavelRules: {
        attrNameCn: [
          { required: true, message: '请输入等级名称' }
        ]
      },
      buttonData: [
        {
          label: '添加',
          action: this.onAddClick,
          permission: 'all'
        },
        {
          label: '批量导入模版下载',
          action: this.downFiles,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.medicinalBatchList }),
      dataList: [],
      search: {
        searchText: {
          label: '药材产品批号/工业标识码/药材产品名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入药材产品批号/工业标识码/药材产品名称'
          }
        },
        dataTime: {
          label: '批次生产时间',
          value: null,
          type: 'date',
          option: {
            type: 'daterange',
            startPlaceholder: this.$t('common.startDate'),
            endPlaceholder: this.$t('common.endDate'),
            unlinkPanels: true,
            placeholder: '请选择时间',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        }
      },

      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '药材产品批号',
            prop: 'medicinalBatchCode',
            sortable: false
          },
          {
            label: '工业标识码',
            prop: 'industrialIdentificationCode',
            sortable: false
          },
          {
            label: '药材产品名称',
            prop: 'medicinalProductName',
            sortable: false
          },
          {
            label: '规格等级',
            prop: 'specificationGrade',
            sortable: false
          },
          {
            label: '药材统计单位',
            prop: 'statisticUnits',
            sortable: false
          },
          {
            label: '数量',
            prop: 'number',
            sortable: false
          },
          {
            label: '批次生产时间',
            prop: 'batchProductionTime',
            sortable: false
          }
        ],
        operation: {
          label: '操作',
          width: '220px',
          data: (row) => {
            return row.industrialIdentificationCode
              ? [
                {
                  label: '工业码',
                  action: this.onOpenCode,
                  permission: 'all'
                }, {
                  label: '质检信息',
                  action: this.addPdf,
                  permission: 'all'
                },
                {
                  label: '详情',
                  action: this.onDetail,
                  permission: 'all'
                },
                {
                  label: '编辑',
                  action: this.onEditClick,
                  permission: 'all'
                }, {
                  label: '删除',
                  action: this.onDeleteClick,
                  permission: 'all'
                }] : [{
                label: '质检信息',
                action: this.addPdf,
                permission: 'all'
              },
              {
                label: '详情',
                action: this.onDetail,
                permission: 'all'
              },
              {
                label: '编辑',
                action: this.onEditClick,
                permission: 'all'
              }, {
                label: '删除',
                action: this.onDeleteClick,
                permission: 'all'
              }]
          }

        }
      },
      showVisible: false,
      imgFormModel: {
        fileList: [],
        remark: ''
      },
      imgFormRules: {
        fileList: { required: true, message: '请上传图片' },
        remark: { required: true, message: '请输入图片说明' }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  methods: {

    // 打开弹窗
    addPdf(row) {
      this.visible = true
      this.pdfId = row.id
      if (row.medicinalBatchFileList.length > 0) {
        this.fileInfo = {
          name: row.medicinalBatchFileList[0].fileName,
          url: row.medicinalBatchFileList[0].filePath
        }
      } else {
        this.fileInfo = null
      }
    },
    onUploadPdfMany(file, fileList) {
      console.log(file, fileList)
      const formData = new FormData()
      formData.append('file', file.raw)
      accApi.importMedicinalBatchApi(formData).then((res) => {
        this.$message.success(res.message)
        this.searchHelper.handleQuery()
      })
    },
    // 上传前验证
    beforeUpload(file) {
      console.log(file)
      const isPDF = file.file.type === 'application/pdf'
      if (!isPDF) {
        this.$message.error('只能上传PDF文件！')
        return false
      }
      const formData = new FormData()
      formData.append('file', file.file)
      commonApi.uploadImg(formData).then((res) => {
        this.fileInfo = {
          name: res.data.fileName,
          url: res.data.url
        }
      })
    },
    // 删除文件
    handleOpenPdf(url) {
      window.open(url)
    },

    // 重新上传
    handleReupload() {
      this.fileInfo = null
    },

    // 保存
    handleSave() {
      if (!this.fileInfo) {
        this.$message.warning('请先上传文件')
        return
      }
      accApi.medicinalBatchFile({
        medicinalBatchId: this.pdfId,
        filePath: this.fileInfo.url,
        fileName: this.fileInfo.name
      }).then((res) => {
        this.$message.success(res.message)
        this.searchHelper.handleQuery()
      })
      this.visible = false
      this.fileInfo = null
    },
    handleRemove() {
      const link = document.createElement('a')
      link.href = this.fileInfo.url
      link.download = 'filename.pdf' // 指定下载文件的名称
      document.body.appendChild(link)
      link.click() // 触发点击事件
      document.body.removeChild(link) // 移除链接元素
    },
    submit() {
      this.$refs['attr-table'].validate(attrValid => {
        if (attrValid) {
          this.$refs['attr-type'].validate(attrValid => {
            if (attrValid) {
              accApi.editDrug({
                id: this.baseFrom.id,
                specificationName: JSON.stringify(this.specTableData),
                specificationGrade: JSON.stringify(this.leavelTableData)
              }).then(res => {
                this.$message.success(res.message)
                this.beianDialogVisible = false
                this.searchHelper.handleQuery()
              })
            }
          })
        }
      })
    },
    onAddClick() {
      this.$router.replace({ path: 'drugBatchNoDetail', query: { action: 'add' }})
    },
    onDeleteClick(row) {
      this.$confirm('请确认是否删除?', '提示').then(() => {
        accApi.delMedicinalBatch(row).then(res => {
          this.$message.success(res.message)
          this.searchHelper.handleQuery()
        })
      })
    },
    onDetail(row) {
      this.$router.replace({ path: 'drugBatchNoDetail', query: { action: 'detail', id: row.id }})
    },
    onEditClick(row) {
      this.$router.replace({ path: 'drugBatchNoDetail', query: { action: 'edit', id: row.id }})
    },
    onOpenCode(row) {
      this.showVisible = true
    },

    // 下载
    downFiles() {
      accApi.exportMedicinalBatchApi({ ...this.searchHelper.getSearchParams() })
    },
    //  导入
    onChange(file, fileList) {
      console.log(file, fileList)
      const formData = new FormData()
      formData.append('file', file.raw)
      accApi.importMedicinalBatchApi(formData).then((res) => {
        this.$message.success(res.message)
        this.searchHelper.handleQuery()
      })
    }

  }
}
</script>

<style scoped lang="scss">
::v-deep .el-divider__text{
  font-weight: bolder;
  color: #004DA1;
}
::v-deep .el-divider{
 background-color: black;
}
::v-deep .el-query{
display: flex;
  gap: 10px;
}
.button-container {
  line-height: 60px;
  text-align: center;
}
.quality-dialog {
  .upload-container {
    min-height: 200px;
    border-radius: 6px;
  }

  .upload-area {
    height: 100%;
    .upload-content {
      text-align: center;
      padding: 40px 0;
      .el-icon-upload {
        font-size: 48px;
        color: #909399;
      }
      .upload-text {
        margin-top: 10px;
        color: #606266;
      }
    }
  }
  .file-preview {
    .preview-item {
      //display: flex;

      align-items: center;
      padding: 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      .pdf-preview {
        height: 250px;
        border: 1px solid #ebeef5;
        overflow: hidden;
        iframe {
          border: none;
        }
      }
      .pdf-icon {
        width: 32px;
        height: 32px;
        margin-right: 10px;
      }

      .file-name {
        width: 100%;
        height: 34px;
        background-color: #EBEBEB;
        overflow: hidden;
        padding:0 10px;
        line-height: 34px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .close-icon {
        cursor: pointer;
        color: #909399;

        &:hover {
          color: #f56c6c;
        }
      }
    }
    .preview-item-move{
      background-color: rgba(10,12,12,0.3);
      width: calc(100% - 40px);
      height: 250px;
      box-sizing: border-box;
      position: absolute;top: 10px;
      left: 10px; opacity: 0;
      transition: opacity 0.2s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }
    .preview-item-move:hover{
      opacity: 1;
    }
    .reupload-btn {
      background-color: #fff;
      color: black;
      //margin-bottom: 10px;
      border-color: #fff;
    }
  }}

</style>
